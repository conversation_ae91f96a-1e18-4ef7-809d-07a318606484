<!--index.wxml-->
<view class="container">
    <view class="head">
        <image class="user-avatar" src="../../static/images/logo.png" mode="" />
        <view class="head-Nameinfo">
            <text>{{userInfo.PersonName}}</text>
        </view>
        <view class="head-Nameinfo">
            账号：{{userInfo.PersonId}}
        </view>
        <view class="head-Nameinfo">
            <view>
                电话号码：{{userInfo.Phone}}
            </view>
        </view>
    </view>
    <view class="mybody">
        当前公司：
        <radio-group bindchange="radioChange">
            <label class="radioinfo" wx:for="{{userInfo.Company}}" wx:key="CompanyId">
                <view class="">
                    <radio value="{{item.CompanyId}}" checked="{{item.IsMain}}" />
                </view>
                <view class="">{{item.CompanyName}}</view>
            </label>
        </radio-group>
    </view>
    <view class="mybody">
        当前角色：
        <text wx:for="{{userInfo.Roles}}" wx:key="RoleId"> {{item.RoleName}} </text>
    </view>

    <!-- 功能选择区域 -->
    <view class="function-section">
        <view class="function-title">请选择功能模块</view>
        <view class="button-container">
            <button type="primary" bindtap="goToVehicleMaintenance" class="function-btn">
                车辆维保
            </button>
            <button type="primary" bindtap="goToFeedback" class="function-btn">
                现场信息反馈
            </button>
        </view>
    </view>

    <view class="logout-section">
        <button type="primary" size="default" bindtap="AgainLogin">重新登陆</button>
    </view>

</view>