/**index.wxss**/
page{
    background-color: rgb(223, 223, 223);
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* 主容器 */
.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    width: 100%;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200px;
}

.head{
    padding: 50rpx 0 40rpx 0;
    min-height: 400rpx;
    max-height: 35vh;
    background-color: rgb(18, 150, 219);
    text-align: center;
    color: white;
    font-size: large;
    font-weight: bolder;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    margin: 0;
}

.user-avatar
{
    width: 160rpx;
    height: 160rpx;
    min-width: 120rpx;
    min-height: 120rpx;
    max-width: 200rpx;
    max-height: 200rpx;
    border-radius: 50%;
    background-color: rgb(233, 233, 233);
    flex-shrink: 0;
}
.head-Nameinfo
{
    margin: 15rpx 20rpx;
    font-size: 28rpx;
    line-height: 1.4;
    word-break: break-all;
    max-width: 90%;
}
.mybody{
    margin: 20rpx 5% 0 5%;
    background-color: white;
    font-size: 28rpx;
    padding: 30rpx 20rpx;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    word-break: break-all;
}
.radioinfo
{
    display: flex;
}
.radioinfo view
{
    margin-top: 30rpx;
}

/* 功能选择区域样式 */
.function-section {
    margin: 20rpx 5% 0 5%;
    background-color: white;
    border-radius: 20rpx;
    padding: 30rpx 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

.function-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 30rpx;
}

.button-container {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.function-btn {
    width: 100%;
    min-height: 80rpx;
    font-size: 32rpx;
    color: white;
    background-color: #1296DB !important;
    box-sizing: border-box;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 覆盖微信小程序默认的 primary 按钮样式 */
button[type="primary"] {
    background-color: #1296DB !important;
    border-color: #1296DB !important;
}

button[type="primary"]:not([disabled]):active {
    background-color: #0d7bc4 !important;
    border-color: #0d7bc4 !important;
}

/* 覆盖 radio 选中状态的绿色样式 */
radio .wx-radio-input.wx-radio-input-checked {
    background-color: #1296DB !important;
    border-color: #1296DB !important;
}

radio .wx-radio-input.wx-radio-input-checked::before {
    color: white !important;
}

/* 重新登陆按钮区域样式 */
.logout-section {
    margin: 20rpx 5% 20rpx 5%;
    box-sizing: border-box;
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 400px) {
    .head {
        min-height: 350rpx;
        padding: 40rpx 0 30rpx 0;
    }

    .user-avatar {
        width: 140rpx;
        height: 140rpx;
    }

    .head-Nameinfo {
        font-size: 26rpx;
        margin: 12rpx 15rpx;
    }

    .mybody, .function-section, .logout-section {
        margin-left: 3%;
        margin-right: 3%;
        padding: 25rpx 15rpx;
    }

    .function-title {
        font-size: 32rpx;
    }

    .function-btn {
        font-size: 30rpx;
        min-height: 75rpx;
    }
}

/* 响应式设计 - 大屏幕适配 */
@media (min-width: 800px) {
    .head {
        max-height: 30vh;
        min-height: 450rpx;
        padding: 60rpx 0 50rpx 0;
    }

    .user-avatar {
        width: 180rpx;
        height: 180rpx;
    }

    .head-Nameinfo {
        font-size: 30rpx;
        margin: 18rpx 25rpx;
    }

    .mybody, .function-section {
        max-width: 600rpx;
        margin-left: auto;
        margin-right: auto;
        margin-top: 20rpx;
    }

    .logout-section {
        max-width: 600rpx;
        margin-left: auto;
        margin-right: auto;
        margin-top: 20rpx;
        margin-bottom: 20rpx;
    }
}